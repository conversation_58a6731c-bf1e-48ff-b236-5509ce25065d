import 'package:flutter/material.dart';
import 'package:flutter_smarthome/core/utils/navigation_controller.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'dart:io';
import 'app/base_tabbar_controller.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oktoast/oktoast.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'core/utils/user_manager.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:fluwx/fluwx.dart' as fluwx;
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'core/services/firebase_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化 Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // 初始化 Firebase 服务
  await FirebaseService.instance.initialize();

  await UserManager.instance.init();

  // 设置导航监听
  await NavigationController.setupNavigationHandler();

  // 全局设置证书验证豁免
  HttpOverrides.global = MyHttpOverrides();

  runApp(
    const OKToast(
      child: MyApp(),
    ),
  );
  _registerWxApi(); // 注册微信 API
}

//注册微信 API
void _registerWxApi() {
  // 创建Fluwx实例并调用registerApi方法
  final fluwxInstance = fluwx.Fluwx();
  fluwxInstance.registerApi(
    appId: "wx8531759f373d8a56",
    doOnAndroid: true,
    doOnIOS: true,
    universalLink: "https://crs.gazolife.cn/ios/",
  );
  // .then((success) { // registerApi 返回 Future<bool>，可以处理结果
  //   if (success) {
  //     print("WXAPI 注册成功");
  //   } else {
  //     print("WXAPI 注册失败");
  //   }
  // }).catchError((e) {
  //    print("WXAPI 注册时发生错误: $e");
  // });
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp(
          navigatorKey: NavigationController.navigatorKey,
          debugShowCheckedModeBanner: false,
          localizationsDelegates: const [
            RefreshLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('zh', 'CN'),
            Locale('en', 'US'),
          ],
          home: FutureBuilder<bool>(
            future: _checkIfLoggedIn(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Scaffold(
                  body: Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              }
              return const BaseTabBarController();
            },
          ),
          builder: EasyLoading.init(),
        );
      },
    );
  }

  Future<bool> _checkIfLoggedIn() async {
    final user = UserManager.instance.user;
    return user != null;
  }
}
